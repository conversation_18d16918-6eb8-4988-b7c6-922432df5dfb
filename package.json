{"name": "kyctips", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "dev:test": "vite --mode test", "dev:staging": "vite --mode staging", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:staging": "vite preview --mode staging", "preview:prod": "vite preview --mode production", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "clean": "rm -rf dist", "clean:build": "npm run clean && npm run build:prod", "test:env": "node scripts/test-env.cjs", "build-only": "vite build"}, "dependencies": {"axios": "^1.8.4", "crypto-js": "^4.2.0", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.19.3", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "sass": "^1.69.7", "typescript": "~5.3.0", "vite": "^5.0.10", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^1.8.25"}}