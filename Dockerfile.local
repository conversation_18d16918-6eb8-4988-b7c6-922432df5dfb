FROM node:22.14.0-slim AS build

ARG buildEnv=dev
ENV buildEnv=${buildEnv}

WORKDIR /opt/www/h5
COPY . /opt/www/h5

# npm run build:${buildEnv}
RUN npm install --ignore-engines --prefer-offline \
    --registry=https://registry.npmmirror.com && \
    npm run build-only

FROM nginx:1.26.0

WORKDIR /opt/www/h5
COPY --from=build /opt/www/h5/dist .
RUN chown -R nginx:nginx /opt/www && chmod -R 755 /opt/www

EXPOSE 80 443