# 环境配置文件

本文件夹包含项目的所有环境配置文件，用于统一管理不同环境的配置。

## 文件结构

```
env/
├── .env                 # 默认环境配置
├── .env.development     # 开发环境配置
├── .env.test           # 测试环境配置
├── .env.staging        # 预发环境配置
├── .env.production     # 生产环境配置
└── README.md           # 本说明文件
```

## 环境变量说明

每个环境配置文件都包含以下必需的环境变量：

- `VITE_APP_TITLE`: 应用标题
- `VITE_APP_ENV`: 当前环境标识
- `VITE_BASE_URL`: API 基础地址
- `VITE_API_TIMEOUT`: API 请求超时时间
- `VITE_PROXY_TARGET`: 代理目标地址
- `VITE_JUMP_URL`: 跳转地址
- `VITE_ASSETS_URL`: 静态资源地址

## 使用方法

### 开发时
```bash
npm run dev              # 使用 .env.development
npm run dev:test         # 使用 .env.test
npm run dev:staging      # 使用 .env.staging
```

### 构建时
```bash
npm run build:test       # 使用 .env.test
npm run build:staging    # 使用 .env.staging
npm run build:prod       # 使用 .env.production
```

### 检查配置
```bash
npm run test:env         # 检查所有环境配置文件
```

## 注意事项

1. **文件安全**: 不要在环境配置文件中存储敏感信息
2. **版本控制**: 环境配置文件已加入版本控制，本地配置请使用 `.env.local` 文件
3. **配置优先级**: Vite 会按照以下优先级加载环境变量：
   - `.env.[mode].local`
   - `.env.local`
   - `.env.[mode]`
   - `.env`

## 配置更新

如果需要添加新的环境变量：

1. 在所有相关的环境配置文件中添加变量
2. 在 `env.d.ts` 中添加类型声明
3. 在 `src/utils/env.ts` 中添加相应的工具函数
4. 运行 `npm run test:env` 验证配置
