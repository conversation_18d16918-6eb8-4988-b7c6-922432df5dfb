html {
  scroll-padding-top: 200px;
  /* 等于导航条高度 */
  /* scroll-behavior: smooth; */
}

body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  /* width: 100vw; */
  overflow-x: hidden;
  box-sizing: border-box;
}

h4 {
  margin: 0.8rem;
}

/* Header Styles */
header {
  height: 216px;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: white;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
}

nav {
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  padding: 20px 40px 33px;
  transition: all 0.3s ease;
  background: #fff;
  box-sizing: border-box;
}

nav.scrolled {
  background: #fff;
  backdrop-filter: blur(10px);
  /* padding: 15px 40px; */
}

nav img {
  height: 40px;
}

#we-logo {
  /* height: 100px; */
  /* flex-grow: 1; */
  text-align: center;
  width: 100%;
  /* display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px; */
}

#we-logo img {
  width: 14vw;
  height: auto;
}

nav .menu {
  display: none;
}

nav ul {
  width: 100%;
  display: flex;
  justify-content: center;
  text-align: center;
  list-style: none;
  padding: 0;
  margin: 52px 0 0;
  gap: 20px;
  font-size: 15px;
}

nav a {
  color: #AC1140;
  text-decoration: none;
  font-weight: bold;
  transition: opacity 0.3s;
  font-weight: 400;
  white-space: nowrap;
  cursor: pointer;
}

nav a:hover {
  opacity: 0.8;
}

.nav-menu .active {
  border-bottom: 1px solid #AC1140;
}

.header-content {
  position: relative;
}


.header-content img {
  width: 100%;
  height: auto;
}

.header-content .text {
  text-align: center;
  position: absolute;
  font-size: 16px;
  top: 50%;
  left: 50%;
  width: 80%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-family: emoji;

}

.header-content .text h2 {
  font-size: 66px;
  margin: 1vh 0 2vh;
  font-family: math;
}

.MissionVision {
  padding: 4vh 12vw;
  display: flex;
  font-size: 15px;
  text-align: left;
}

.MissionVision img {
  width: 42%;
  height: auto;
}

.MissionVision .text {
  width: 48%;
}

.MissionVision h1 {
  text-align: left;
  margin: 0 0 20px;
  font-size: 36px;
  font-weight: 400;
}

.products {
  padding: 2vh 10vw;

}

.products h1 {
  margin: 0 0 40px;
  font-size: 36px;
  font-style: normal;
  font-weight: 400;
  color: #000;
}

.products h4 {
  color: #000;
  margin: 0 0 10px;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
}

.products div {
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  color: #646868;
  margin-bottom: 20px;
}

.products ul {
  margin: 0;
  padding: 0 0 0 10px;
}

.product-img {
  margin-top: 3vh;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
}

.product-img div {
  width: 48%;
  min-height: 25vh;
  text-align: center;
  padding-top: 14vh;
  border-radius: 10px;
  color: #fff;
  font-size: 60px;
  font-style: normal;
  font-weight: 400;
  background: url('imgs/table.png') no-repeat;
  margin-bottom: 0;
  box-sizing: border-box;
}

.aboutUs,
.contactUs,
.joinUs,
.cooperative {
  padding: 4vh 10vw;
  color: #646868;
}

.aboutUs p,
.aboutUs h1,
.contactUs p,
.joinUs>div,
.contactUs div,
.cooperative div {
  text-align: left;
  padding: 0;
  margin: 0 0 2vh !important;
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
}

.aboutUs h1,
.contactUs h1,
.joinUs h1 {
  font-size: 36px;
  color: #000;
  font-weight: 400;
}

.contactUs img {
  vertical-align: middle;
}

.contactUs p:nth-child(3) {
  text-indent: 2em;
}

.joinUs-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.joinUs-list>a {
  text-decoration: none;
  display: block;
  width: 36vw;
  color: #333;
  background-color: #FAFBFC;
  padding: 30px 30px 60px;
  text-align: left;
  box-sizing: border-box;
  position: relative;
}

.joinUs-list a p {
  text-align: left;
  margin: 0;
  padding: 0;
  color: #383838;
  position: absolute;
  bottom: 20px;
}

.joinUs-list h2 {
  font-size: 28px;
  font-style: normal;
  font-weight: 500;
  color: #000;
  text-align: left;
  margin: 0;
  padding: 0 0 14px;
}

.joinUs-list a ul {
  color: #676767;
  padding-left: 20px;
}

.joinUs-list img {
  vertical-align: sub;
}

.shadow-back {
  background-color: #F7F7F7;
}

.shadow-back>div {
  /* max-width: 1200px; */
  max-width: 100vw;
  margin: 0 auto;
  padding: 40px 12px;
  box-sizing: border-box;
}

.cooperative>h1 {
  text-align: left;
  font-size: 36px;
  margin-bottom: 50px;
  font-weight: 400;
}

/* Cooperative Section */
.cooperative-list {
  display: grid;
  grid-template-columns: repeat(5, minmax(auto, 1fr));
  gap: 12px;
}

.cooperative-item {
  max-width: 230px;
  height: 110px;
  border-radius: 4px;
}

.cooperative-item img {
  object-fit: contain;
  width: 100%;
}

/* Footer Styles */
footer {
  background-color: #333;
  color: white;
  padding: 26px 0;
}

.footes {
  max-width: 1200px;
  margin: 0 10vw;
  padding: 40px 0 38px;
  display: flex;
  box-sizing: border-box;
  color: #898A8D;
  justify-content: space-between;
}

.footes .email img {
  vertical-align: middle;
}

.footes .mylogs {
  margin-bottom: 32px;
}

.fotbtn {
  padding: 27px 0 0;
  text-align: center;
  color: #898A8D;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  width: 100%;
  border-top: 1px solid #646868;
}


.rightnav {
  display: flex;
  gap: 40px;
  /* margin-left: auto; */
}

.rightnav>div {
  flex: 1;
}

.rightnav>div>p {
  font-size: 18px;
  color: #FFF;
  margin-bottom: 10px;
  text-align: left;
}

/* Section Styles */
section {
  padding: 20px 0;
}

section>h2,
section>div,
section>p {
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  font-size: 20px;
  margin-bottom: 30px;
  text-align: center;
}



.mylogs {
  display: flex;
  align-items: center;
  max-width: 100%;
  justify-content: center;
}

.mylogs>img {
  flex: 1;
  max-width: 45%;
  height: auto;
  min-height: 35px;
  margin-right: 20px;
  object-fit: contain;
  max-width: 180px;
}

.mylogs>img:last-child {
  margin-right: 0;
  object-fit: contain;
  flex: 1;
  max-width: 200px;
}


.navul>div {
  display: flex;
}

.navul>div>ul {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 15px;
  color: #7A7C80;
  cursor: pointer;
}

.navul>div>ul>li {
  margin-bottom: 10px;
  white-space: nowrap;
}

.navul>div>ul>li a {
  text-decoration: none;
  color: #7A7C80;
}

.navul>div>ul:last-child {
  margin-left: 20px;
}

.menu-icon,
.small-logo {
  display: none;
}




.menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  transition: opacity 0.3s ease;
}

.menu-overlay.show {
  display: block;
}

/* Contact Sidebar Styles */
.contact-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s ease;
}

.contact-sidebar.show {
  right: 0;
}

.contact-form {
  padding: 30px;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.close-btn {
  position: absolute;
  right: 20px;
  top: 20px;
  height: 32px;
  width: 32px;
  background-color: #AC1140;
  /* background: none; */
  border-radius: 50%;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #fff;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 15px 16px;
  /* border: 1px solid #ddd; */
  border-radius: 4px;
  box-sizing: border-box;
  border: none;
  background-color: #F8F8F8;
}

.form-group textarea {
  height: 120px;
  resize: vertical;
}

.contact-form button[type="submit"] {
  background: #333;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
}

.contact-form button[type="submit"]:hover {
  background: #444;
}

@media (max-width: 1000px) {
  .contact-sidebar {
    width: 100%;
    right: -100%;
  }
}

/* Back to Top Button Styles */
.back-to-top-wrapper {
  position: sticky;
  bottom: 40px;
  /* 距离底部40px */
  height: 0;
  /* 避免占用空间 */
}

.back-to-top {
  position: absolute;
  right: 0;
  bottom: 40px;
  width: 50px;
  height: 50px;
  background-color: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 99;
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top img {
  width: 24px;
  height: 24px;
}

@media (max-width: 1000px) {
  .back-to-top-wrapper {
    margin-right: 20px;
    bottom: 20px;
  }

  .back-to-top {
    width: 40px;
    height: 40px;
  }

  .back-to-top img {
    width: 20px;
    height: 20px;
  }
}

.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.page-loader.loaded {
  opacity: 0;
  pointer-events: none;
}

.loader {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}



/* detail */
.detail-page {
  background-color: #F6F7F9;
}

.detail-header {
  position: relative;
  background: #FFF;
  text-align: center;
  width: 100vw;
  height: auto;
}

.detail-header img {
  width: 200px;
  height: auto;
  margin: 0 auto;
}

.detail-page .backBtn {
  position: absolute;
  top: 10px;
  left: 40px;
  color: #AC1140;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 36px;
  width: 80vw;
  text-align: left;
  margin: 20px auto;
  text-decoration: none;
}

.detail-page .content {
  padding: 20px 16vw;
  display: none;
}

.detail-page .content img {
  vertical-align: sub;
}

.detail-page .content.show {
  display: block;
}

.detail-page .title-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-page ul {
  color: #676767;
  overflow-wrap: break-word;
  word-break: break-word;
}

.detail-page .submit {
  background-color: #AC1140;
  color: #fff;
  width: 267px;
  height: 73px;
  border: none;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
}

.upload-form {
  display: flex;
  align-items: center;
  background-color: #F8F8F8;
  height: 48px;
  gap: 10px;
  padding-left: 10px;
  /* line-height:48px; */
}

.upload-form .custom-button {
  width: 24px;
  height: 24px;
  background: #AC1140;
  color: #fff;
  border-radius: 50%;
  text-align: center;
}

.upload-form>span {
  color: #9098A2;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
}

.upload-form>span.hasFile {
  color: #000;
}


/* Responsive Styles 小屏样式 */
@media (max-width: 1000px) {
  header {
    height: auto;
  }

  nav {
    /* height: 44px; */
    padding: 10px 20px;
    /* overflow: hidden; */
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }

  #we-logo {
    display: none;
  }

  nav ul {
    display: none;
  }

  .header-content {
    height: 576px;
  }

  .header-content .text {
    font-size: 14px !important;
  }

  .header-content .text h2 {
    font-size: 25px;
  }

  .header-content img {
    height: 100%;
  }

  .small-header {
    display: flex;
    justify-content: space-between;
    position: relative;
    width: 100vw;
    align-items: center;
  }

  section {
    margin-top: 0;
    padding: 2vh 8vw !important;

  }

  .MissionVision {
    flex-direction: column;
    justify-content: flex-start;
    flex-grow: 1;
  }

  .MissionVision img,
  .MissionVision .text {
    width: 76vw;
    margin: 10px 0;
  }

  .products h1 {
    /* font-size: 20px; */
  }

  .products h4 {
    font-size: 16px;
  }

  .product-img {
    flex-direction: column;
    flex-grow: 1;
  }

  .product-img div {
    width: 78vw;
    background-size: cover;
  }

  .joinUs-list {
    flex-direction: column;
  }

  .joinUs-list>a {
    width: 78vw;
  }

  .joinUs-list h2 {
    font-size: 16px;
  }

  .cooperative h1 {
    /* font-size: 20px; */
  }

  .cooperative-list {}

  .cooperative-item {
    height: auto;
    text-align: center;

  }

  .footes {
    padding: 20px;
  }

  .fotbtn {
    font-size: 12px;
  }

  .navul>div>ul>li {
    margin: 0;
  }

  nav .menu {
    display: block;
  }

  .nav-menu {
    /* display: none; */
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .cooperative>h1 {
    margin-bottom: 20px;
  }

  .nav-menu.show {
    display: block;
  }

  .header-content h1 {
    font-size: 2rem;
  }


  .cooperative-list {
    grid-template-columns: repeat(2, 1fr);
  }

  .footes {
    flex-direction: column;
    gap: 20px;
  }

  .mylogs>img,
  .mylogs>img:last-child {
    max-width: 45%;
  }


  .rightnav {
    display: flex;
    gap: 12px;
    margin-left: 0;
  }

  .navul>div {
    display: block;
  }

  .navul>div>ul:last-child {
    margin-left: auto;
  }

  .fot-right {
    flex-direction: column;
    gap: 20px;
  }

  nav ul.nav-menu {
    position: fixed;
    top: 0;
    right: -280px;
    width: 280px;
    height: 100vh;
    background-color: #fff;
    flex-direction: column;
    /* padding: 60px 20px 0; */
    margin: 0;
    box-sizing: border-box;
    z-index: 999;
    transition: right 0.3s ease;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  nav ul.nav-menu::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }

  nav ul.nav-menu li a {
    display: block;
    padding: 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #eee;
  }

  .small-logo {
    display: block;
  }

  .menu-icon {
    display: block;
    cursor: pointer;
    z-index: 1000;
    width: 24px;
    height: 24px;
  }

  .detail-header {
    position: fixed;
    top: 0;
    left: 0;
    padding: 0;
  }

  .detail-page {
    padding-top: 100px;
  }

  .detail-page .content .title-head {
    flex-direction: column;
    font-size: 0.8em;
  }

  .detail-page .content .submit {
    margin-top: 18px;
    width: 200px;
    height: 60px;
    font-size: 20px;
  }

  .detail-page section {
    padding: 0;
  }

  .detail-header img {
    width: 150px;
    height: auto;
    margin: 20px auto;
  }

  .detail-page .content ul {
    font-size: 14px;
  }

  .detail-page .content {
    padding: 20px 10vw;
  }

}