/**
 * 环境配置工具
 */

export type AppEnv = "development" | "test" | "staging" | "production";

/**
 * 获取当前环境
 */
export const getAppEnv = (): AppEnv => {
  return (import.meta.env.VITE_APP_ENV as AppEnv) || "development";
};

/**
 * 判断是否为开发环境
 */
export const isDevelopment = (): boolean => {
  return getAppEnv() === "development";
};

/**
 * 判断是否为测试环境
 */
export const isTest = (): boolean => {
  return getAppEnv() === "test";
};

/**
 * 判断是否为预发环境
 */
export const isStaging = (): boolean => {
  return getAppEnv() === "staging";
};

/**
 * 判断是否为生产环境
 */
export const isProduction = (): boolean => {
  return getAppEnv() === "production";
};

/**
 * 获取应用标题
 */
export const getAppTitle = (): string => {
  return import.meta.env.VITE_APP_TITLE || "Nustar Pay Result";
};

/**
 * 获取 API 基础 URL
 */
export const getBaseURL = (): string => {
  return import.meta.env.VITE_BASE_URL || "https://dev.nustaronline.vip";
};
export const getAssetsUrl = (): string => {
  return import.meta.env.VITE_ASSETS_URL || "https://uat-nustar-static.nustaronline.vip/";
};

/**
 * 获取 API 超时时间
 */
export const getApiTimeout = (): number => {
  return Number(import.meta.env.VITE_API_TIMEOUT) || 30000;
};

/**
 * 获取跳转 URL
 */
export const getJumpURL = (): string => {
  return import.meta.env.VITE_JUMP_URL || "https://dev-h5.nustaronline.vip/?debug_web_login=1";
};

/**
 * 是否启用控制台日志
 */
export const isConsoleEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_CONSOLE === "true";
};

/**
 * 是否启用开发者工具
 */
export const isDevtoolsEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_DEVTOOLS === "true";
};

/**
 * 是否启用 Mock 数据
 */
export const isMockEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_MOCK === "true";
};

/**
 * 环境配置对象
 */
export const envConfig = {
  env: getAppEnv(),
  title: getAppTitle(),
  baseURL: getBaseURL(),
  assetsUrl: getAssetsUrl(),
  jumpURL: getJumpURL(),
  timeout: getApiTimeout(),
  isDev: isDevelopment(),
  isTest: isTest(),
  isStaging: isStaging(),
  isProd: isProduction(),
  enableConsole: isConsoleEnabled(),
  enableDevtools: isDevtoolsEnabled(),
  enableMock: isMockEnabled(),
};

/**
 * 打印环境信息
 */
export const printEnvInfo = (): void => {
  if (isConsoleEnabled()) {
    console.group("🌍 Environment Info");
    console.log("Environment:", envConfig.env);
    console.log("Title:", envConfig.title);
    console.log("Base URL:", envConfig.baseURL);
    console.log("Jump URL:", envConfig.jumpURL);
    console.log("Timeout:", envConfig.timeout);
    console.log("Console Enabled:", envConfig.enableConsole);
    console.log("Devtools Enabled:", envConfig.enableDevtools);
    console.log("Mock Enabled:", envConfig.enableMock);
    console.groupEnd();
  }
};
