# App下载引导页

这是一个功能完整的app下载引导页，支持iOS和Android平台的应用下载。

## 功能特性

### 1. 动态配置
- 页面内容（背景图片、标语）通过后台配置读取
- 支持自定义按钮文本和下载链接
- 配置加载失败时自动使用默认配置

### 2. 智能设备识别
- 自动检测用户设备类型（iOS/Android/Desktop）
- 根据设备类型优化显示效果
- 支持微信、QQ等应用内浏览器检测

### 3. 多级下载策略
1. **优先尝试应用商城**：首先尝试打开App Store或Google Play
2. **备用直接下载**：应用商城打开失败时，自动尝试直接下载安装包
3. **错误提示**：下载失败或超时时显示友好的错误提示

### 4. 用户体验优化
- 响应式设计，适配各种屏幕尺寸
- 支持深色模式
- 流畅的动画效果和交互反馈
- 底部固定下载模块，不影响内容浏览

## API接口

### 获取页面配置
```
GET /api/app/download/config
```

响应格式：
```json
{
  "code": 200,
  "data": {
    "backgroundImage": "https://example.com/bg.jpg",
    "slogan": "Download our app for the best experience!",
    "iosButtonText": "Download for iOS",
    "androidButtonText": "Download for Android"
  }
}
```

### 获取下载链接
```
GET /api/app/download/links
```

响应格式：
```json
{
  "code": 200,
  "data": {
    "ios": {
      "storeUrl": "https://apps.apple.com/app/your-app",
      "downloadUrl": "https://download.example.com/app.ipa"
    },
    "android": {
      "storeUrl": "https://play.google.com/store/apps/details?id=your.package",
      "downloadUrl": "https://download.example.com/app.apk"
    }
  }
}
```

## 使用方法

### 1. 路由访问
访问 `/download` 路径即可打开下载页面。

### 2. 自定义配置
修改 `getDefaultConfig()` 函数中的默认配置：

```typescript
function getDefaultConfig(): DownloadConfig {
  return {
    backgroundImage: "", // 背景图片URL，为空时使用CSS渐变
    slogan: "您的自定义标语",
    iosButton: {
      text: "iOS下载",
      storeUrl: "您的App Store链接",
      downloadUrl: "您的iOS安装包下载链接",
    },
    androidButton: {
      text: "Android下载", 
      storeUrl: "您的Google Play链接",
      downloadUrl: "您的Android安装包下载链接",
    },
  };
}
```

### 3. 样式自定义
修改 `<style>` 部分的CSS变量和样式来自定义外观。

## 技术实现

### 设备检测
- 使用 `navigator.userAgent` 检测设备类型
- 支持iOS、Android、桌面设备识别
- 检测微信、QQ等特殊浏览器环境

### 下载逻辑
1. **应用商城跳转**：使用 `window.location.href` 尝试打开应用商城
2. **跳转检测**：通过 `visibilitychange` 事件检测是否成功跳转
3. **直接下载**：创建隐藏的 `<a>` 标签触发下载
4. **超时处理**：设置合理的超时时间，避免用户长时间等待

### 错误处理
- API请求失败时自动使用默认配置
- 下载失败时显示友好的错误提示
- 支持重试机制

## 浏览器兼容性

- 现代浏览器（Chrome 60+, Firefox 60+, Safari 12+）
- 移动端浏览器
- 微信内置浏览器
- QQ内置浏览器

## 注意事项

1. **HTTPS要求**：某些下载功能需要在HTTPS环境下才能正常工作
2. **跨域问题**：确保API接口支持跨域请求
3. **应用商城链接**：确保应用商城链接格式正确
4. **安装包大小**：建议安装包不要过大，避免下载超时
