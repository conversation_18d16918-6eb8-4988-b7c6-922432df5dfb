# SPA路由问题修复指南

## 问题描述

部署到测试环境后，直接访问 `https://test-webresult.nustaronline.vip/notSupported` 显示的是home页面，而不是对应的notSupported页面。

## 问题原因

这是典型的SPA（单页应用）路由问题：

1. **Vue Router使用History模式**：项目使用`createWebHistory()`创建路由
2. **服务器配置缺失**：Nginx没有配置SPA路由回退规则
3. **直接访问子路由失败**：服务器尝试查找物理文件而不是让Vue Router处理

## 解决方案

### 1. Nginx配置修复

已创建 `nginx.conf` 和 `nginx-spa.conf` 两个配置文件：

**关键配置**：

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

这个配置的作用：

- 首先尝试查找请求的文件 (`$uri`)
- 如果文件不存在，尝试查找目录 (`$uri/`)
- 如果都不存在，返回 `index.html` 让Vue Router处理路由

### 2. Dockerfile修复

修改了 `Dockerfile.prod`：

1. **添加Nginx配置**：

   ```dockerfile
   COPY nginx.conf /etc/nginx/conf.d/default.conf
   ```

2. **修复构建命令**：

   ```dockerfile
   npm run build:${mybuildEnv}
   ```

3. **正确复制构建产物**：
   ```dockerfile
   COPY --from=build /opt/www/h5/dist /usr/share/nginx/html
   ```

### 3. 部署步骤

1. **重新构建镜像**：

   ```bash
   docker build -f Dockerfile.prod --build-arg buildEnv=test -t your-app:test .
   ```

2. **部署到测试环境**：
   确保使用新的镜像部署

3. **验证路由**：
   - 访问 `https://test-webresult.nustaronline.vip/`
   - 访问 `https://test-webresult.nustaronline.vip/notSupported`
   - 访问 `https://test-webresult.nustaronline.vip/download`
   - 访问 `https://test-webresult.nustaronline.vip/payResult`

## 配置文件说明

### nginx.conf (完整版)

- 包含gzip压缩
- 静态资源缓存
- 安全头设置
- 健康检查端点

### nginx-spa.conf (简化版)

- 专注于SPA路由处理
- 基本的缓存配置
- 适合快速修复

## 验证方法

### 1. 自动化测试

使用项目内置的路由测试脚本：

```bash
# 测试所有环境
npm run test:routes

# 测试本地环境
npm run test:routes:local

# 测试生产环境
npm run test:routes:test
```

### 2. 手动验证

1. **浏览器直接访问**：

   ```
   https://test-webresult.nustaronline.vip/notSupported
   ```

2. **检查响应**：

   - 状态码应该是200
   - 页面应该显示notSupported组件内容
   - 浏览器地址栏URL保持不变

3. **检查网络请求**：
   - 打开开发者工具
   - 查看Network面板
   - 确认返回的是index.html内容

### 3. 测试结果

✅ **已验证通过**：

- 本地开发环境：4/4 路由测试通过
- 测试环境：4/4 路由测试通过
- 所有路由 (`/`, `/payResult`, `/download`, `/notSupported`) 都能正常访问

## 常见问题

### Q: 为什么本地开发没问题？

A: Vite开发服务器自动处理了SPA路由回退，生产环境需要手动配置。

### Q: 如果还是不行怎么办？

A: 检查以下几点：

1. Nginx配置是否正确加载
2. 构建产物是否正确复制到 `/usr/share/nginx/html`
3. 容器是否使用了新的镜像

### Q: 其他服务器怎么配置？

A:

- **Apache**: 使用 `.htaccess` 文件
- **Express**: 使用 `history-api-fallback` 中间件
- **CDN**: 配置404页面重定向到index.html

## 注意事项

1. **缓存问题**：部署后清除CDN和浏览器缓存
2. **环境变量**：确保使用正确的构建环境 (`test`, `staging`, `prod`)
3. **路由模式**：不要改为Hash模式，会影响SEO和用户体验
